-- ==================== 系统初始化数据 ====================
-- 注意：这是初始化脚本，会删除现有数据并重新插入

-- 系统角色表初始化
DELETE FROM sys_roles;
INSERT INTO sys_roles (id, name, role_level, created_at, updated_at)
VALUES
    (1, 'admin', 2, NOW(), NOW()),
    (2, 'normal', 0, NOW(), NOW());

-- 系统用户表初始化
DELETE FROM sys_users;
INSERT INTO sys_users (id, username, password_digest, role_level, status, created_at, updated_at)
VALUES
    (1, 'admin', '$2a$12$Jz8.7h07XTBCIqqE6MFy4uwUuWk7uHkNQMbNCrHpqee.pFcDf1966', 2, 0, NOW(), NOW());

-- 分类表初始化
DELETE FROM categories;
-- 等级分类 (CategoryTypeId = '1')
INSERT INTO categories (name, description, priority, category_type_id, created_at, updated_at)
VALUES
    ('零基础', '适合完全没有英语基础的学习者', 1, '1', NOW(), NOW()),
    ('中级', '适合有一定英语基础的学习者', 2, '1', NOW(), NOW()),
    ('高级', '适合英语基础较好的学习者', 3, '1', NOW(), NOW());

-- 场景分类 (CategoryTypeId = '2')
INSERT INTO categories (name, description, priority, category_type_id, created_at, updated_at)
VALUES
    ('演讲', '公开演讲和演示技巧', 1, '2', NOW(), NOW()),
    ('考试', '各类英语考试准备', 2, '2', NOW(), NOW()),
    ('面试', '求职面试英语技巧', 3, '2', NOW(), NOW()),
    ('出国', '出国留学和生活英语', 4, '2', NOW(), NOW()),
    ('写作', '英语写作技巧和练习', 5, '2', NOW(), NOW());

-- 主题分类 (CategoryTypeId = '3')
INSERT INTO categories (name, description, priority, category_type_id, created_at, updated_at)
VALUES
    ('TED', 'TED演讲学习资源', 1, '3', NOW(), NOW()),
    ('YouTube', 'YouTube视频学习资源', 2, '3', NOW(), NOW()),
    ('博客', '英语博客和文章', 3, '3', NOW(), NOW()),
    ('影视', '电影和电视剧学习资源', 4, '3', NOW(), NOW()),
    ('书籍', '英语书籍和文学作品', 5, '3', NOW(), NOW());

-- 会员等级表初始化
DELETE FROM vips;
INSERT INTO vips (id, name, level, created_at, updated_at)
VALUES
    (1, '普通会员', 1, NOW(), NOW()),
    (2, 'PRO会员', 100, NOW(), NOW()),
    (3, 'ULTRA会员', 1000, NOW(), NOW());

-- 商品表初始化
DELETE FROM trade_products;
INSERT INTO trade_products (
    id,
    name,
    type,
    is_subscription,
    price,
    origin_price,
    ios_product_id,
    currency,
    terminal,
    vip_id,
    days,
    created_at,
    updated_at
)
VALUES
    -- Android端商品
    (1, '月卡', 1, 1, 19.9, 39.9, '', 'RMB', 1, 1, 31, NOW(), NOW()),
    (2, '年卡', 1, 1, 119.9, 339.9, '', 'RMB', 1, 2, 365, NOW(), NOW()),
    (3, '终身会员', 1, 0, 1199.9, 3399.9, '', 'RMB', 1, 3, -1, NOW(), NOW()),
    -- iOS端商品
    (4, '月卡', 1, 1, 38, 39.9, '11', 'RMB', 2, 1, 31, NOW(), NOW()),
    (5, '年卡', 1, 1, 198, 339.9, '12', 'RMB', 2, 2, 365, NOW(), NOW()),
    (6, '终身', 1, 0, 198, 3399.9, '12', 'RMB', 2, 3, -1, NOW(), NOW());

-- 权益组表初始化
DELETE FROM benefit_groups;
INSERT INTO benefit_groups (name, code, status, description, created_at, updated_at)
VALUES
    ('视频大小限制组', 'VIDEO_SIZE_LIMIT', 1, '视频大小限制权益组，包含不同等级的限制', NOW(), NOW()),
    ('AI调用次数组', 'AI_CALLS_LIMIT', 1, 'AI调用次数权益组，包含不同等级的限制', NOW(), NOW()),
    ('字幕对话次数组', 'SUBTITLE_DIALOGUE_LIMIT', 1, '字幕对话次数权益组，包含不同等级的限制', NOW(), NOW());

-- 权益表初始化
DELETE FROM benefits;
-- 视频大小限制组下的不同等级权益
INSERT INTO benefits (
    name,
    code,
    level,
    cycle_type,
    cycle_count,
    benefit_count,
    benefit_group_id,
    benefit_group_name,
    benefit_group_code,
    description,
    status,
    created_at,
    updated_at
)
VALUES
    -- 普通会员视频限制
    (
        '普通会员视频限制',
        'UPLOAD_LIMIT_BASIC',
        10,
        6, -- 无周期
        1,
        500, -- 500MB
        (SELECT id FROM benefit_groups WHERE code = 'VIDEO_SIZE_LIMIT'),
        (SELECT name FROM benefit_groups WHERE code = 'VIDEO_SIZE_LIMIT'),
        'VIDEO_SIZE_LIMIT',
        '普通会员单视频最大500MB',
        1,
        NOW(),
        NOW()
    ),
    -- Pro会员视频限制
    (
        'Pro会员视频限制',
        'UPLOAD_LIMIT_PRO',
        20,
        6, -- 无周期
        100,
        2048, -- 2GB
        (SELECT id FROM benefit_groups WHERE code = 'VIDEO_SIZE_LIMIT'),
        (SELECT name FROM benefit_groups WHERE code = 'VIDEO_SIZE_LIMIT'),
        'VIDEO_SIZE_LIMIT',
        'Pro会员单视频最大2GB',
        1,
        NOW(),
        NOW()
    ),
    -- Ultra会员视频限制
    (
        'Ultra会员视频限制',
        'UPLOAD_LIMIT_ULTRA',
        30,
        6, -- 无周期
        1000,
        5120, -- 5GB
        (SELECT id FROM benefit_groups WHERE code = 'VIDEO_SIZE_LIMIT'),
        (SELECT name FROM benefit_groups WHERE code = 'VIDEO_SIZE_LIMIT'),
        'VIDEO_SIZE_LIMIT',
        'Ultra会员单视频最大5GB',
        1,
        NOW(),
        NOW()
    );

-- AI调用次数组下的不同等级权益
INSERT INTO benefits (
    name,
    code,
    level,
    cycle_type,
    cycle_count,
    benefit_count,
    benefit_group_id,
    benefit_group_name,
    benefit_group_code,
    description,
    status,
    created_at,
    updated_at
)
VALUES
    -- 普通会员AI调用次数
    (
        '普通会员AI调用次数',
        'AI_CALLS_BASIC',
        10,
        3, -- 月周期
        1,
        50, -- 每月50次
        (SELECT id FROM benefit_groups WHERE code = 'AI_CALLS_LIMIT'),
        (SELECT name FROM benefit_groups WHERE code = 'AI_CALLS_LIMIT'),
        'AI_CALLS_LIMIT',
        '普通会员每月50次AI调用',
        1,
        NOW(),
        NOW()
    ),
    -- Pro会员AI调用次数
    (
        'Pro会员AI调用次数',
        'AI_CALLS_PRO',
        20,
        3, -- 月周期
        100,
        200, -- 每月200次
        (SELECT id FROM benefit_groups WHERE code = 'AI_CALLS_LIMIT'),
        (SELECT name FROM benefit_groups WHERE code = 'AI_CALLS_LIMIT'),
        'AI_CALLS_LIMIT',
        'Pro会员每月200次AI调用',
        1,
        NOW(),
        NOW()
    ),
    -- Ultra会员AI调用次数
    (
        'Ultra会员AI调用次数',
        'AI_CALLS_ULTRA',
        30,
        3, -- 月周期
        1000,
        1000, -- 每月1000次
        (SELECT id FROM benefit_groups WHERE code = 'AI_CALLS_LIMIT'),
        (SELECT name FROM benefit_groups WHERE code = 'AI_CALLS_LIMIT'),
        'AI_CALLS_LIMIT',
        'Ultra会员每月1000次AI调用',
        1,
        NOW(),
        NOW()
    );

-- 字幕对话次数组下的不同等级权益
INSERT INTO benefits (
    name,
    code,
    level,
    cycle_type,
    cycle_count,
    benefit_count,
    benefit_group_id,
    benefit_group_name,
    benefit_group_code,
    description,
    status,
    created_at,
    updated_at
)
VALUES
    -- 普通会员字幕对话次数
    (
        '普通会员字幕对话次数',
        'SUBTITLE_DIALOGUE_BASIC',
        10,
        3, -- 月周期
        1,
        100, -- 每月100次
        (SELECT id FROM benefit_groups WHERE code = 'SUBTITLE_DIALOGUE_LIMIT'),
        (SELECT name FROM benefit_groups WHERE code = 'SUBTITLE_DIALOGUE_LIMIT'),
        'SUBTITLE_DIALOGUE_LIMIT',
        '普通会员每月100次字幕对话',
        1,
        NOW(),
        NOW()
    ),
    -- Pro会员字幕对话次数
    (
        'Pro会员字幕对话次数',
        'SUBTITLE_DIALOGUE_PRO',
        20,
        3, -- 月周期
        100,
        500, -- 每月500次
        (SELECT id FROM benefit_groups WHERE code = 'SUBTITLE_DIALOGUE_LIMIT'),
        (SELECT name FROM benefit_groups WHERE code = 'SUBTITLE_DIALOGUE_LIMIT'),
        'SUBTITLE_DIALOGUE_LIMIT',
        'Pro会员每月500次字幕对话',
        1,
        NOW(),
        NOW()
    ),
    -- Ultra会员字幕对话次数
    (
        'Ultra会员字幕对话次数',
        'SUBTITLE_DIALOGUE_ULTRA',
        30,
        3, -- 月周期
        1000,
        2000, -- 每月2000次
        (SELECT id FROM benefit_groups WHERE code = 'SUBTITLE_DIALOGUE_LIMIT'),
        (SELECT name FROM benefit_groups WHERE code = 'SUBTITLE_DIALOGUE_LIMIT'),
        'SUBTITLE_DIALOGUE_LIMIT',
        'Ultra会员每月2000次字幕对话',
        1,
        NOW(),
        NOW()
    );

-- 会员与权益关联初始化
DELETE FROM vip_benefits;
INSERT INTO vip_benefits (
    vip_id,
    vip_level,
    benefit_group_id,
    benefit_id,
    benefit_code,
    create_time,
    create_timestamp,
    created_at,
    updated_at
) VALUES
    -- 普通会员(level=1)的权益
    (
        (SELECT id FROM vips WHERE level = 1),
        1,
        (SELECT id FROM benefit_groups WHERE code = 'VIDEO_SIZE_LIMIT'),
        (SELECT id FROM benefits WHERE code = 'UPLOAD_LIMIT_BASIC'),
        'UPLOAD_LIMIT_BASIC',
        NOW(),
        UNIX_TIMESTAMP(NOW()),
        NOW(),
        NOW()
    ),
    (
        (SELECT id FROM vips WHERE level = 1),
        1,
        (SELECT id FROM benefit_groups WHERE code = 'AI_CALLS_LIMIT'),
        (SELECT id FROM benefits WHERE code = 'AI_CALLS_BASIC'),
        'AI_CALLS_BASIC',
        NOW(),
        UNIX_TIMESTAMP(NOW()),
        NOW(),
        NOW()
    ),
    (
        (SELECT id FROM vips WHERE level = 1),
        1,
        (SELECT id FROM benefit_groups WHERE code = 'SUBTITLE_DIALOGUE_LIMIT'),
        (SELECT id FROM benefits WHERE code = 'SUBTITLE_DIALOGUE_BASIC'),
        'SUBTITLE_DIALOGUE_BASIC',
        NOW(),
        UNIX_TIMESTAMP(NOW()),
        NOW(),
        NOW()
    ),
    
    -- PRO会员(level=100)的权益
    (
        (SELECT id FROM vips WHERE level = 100),
        100,
        (SELECT id FROM benefit_groups WHERE code = 'VIDEO_SIZE_LIMIT'),
        (SELECT id FROM benefits WHERE code = 'UPLOAD_LIMIT_PRO'),
        'UPLOAD_LIMIT_PRO',
        NOW(),
        UNIX_TIMESTAMP(NOW()),
        NOW(),
        NOW()
    ),
    (
        (SELECT id FROM vips WHERE level = 100),
        100,
        (SELECT id FROM benefit_groups WHERE code = 'AI_CALLS_LIMIT'),
        (SELECT id FROM benefits WHERE code = 'AI_CALLS_PRO'),
        'AI_CALLS_PRO',
        NOW(),
        UNIX_TIMESTAMP(NOW()),
        NOW(),
        NOW()
    ),
    (
        (SELECT id FROM vips WHERE level = 100),
        100,
        (SELECT id FROM benefit_groups WHERE code = 'SUBTITLE_DIALOGUE_LIMIT'),
        (SELECT id FROM benefits WHERE code = 'SUBTITLE_DIALOGUE_PRO'),
        'SUBTITLE_DIALOGUE_PRO',
        NOW(),
        UNIX_TIMESTAMP(NOW()),
        NOW(),
        NOW()
    ),
    
    -- ULTRA会员(level=1000)的权益
    (
        (SELECT id FROM vips WHERE level = 1000),
        1000,
        (SELECT id FROM benefit_groups WHERE code = 'VIDEO_SIZE_LIMIT'),
        (SELECT id FROM benefits WHERE code = 'UPLOAD_LIMIT_ULTRA'),
        'UPLOAD_LIMIT_ULTRA',
        NOW(),
        UNIX_TIMESTAMP(NOW()),
        NOW(),
        NOW()
    ),
    (
        (SELECT id FROM vips WHERE level = 1000),
        1000,
        (SELECT id FROM benefit_groups WHERE code = 'AI_CALLS_LIMIT'),
        (SELECT id FROM benefits WHERE code = 'AI_CALLS_ULTRA'),
        'AI_CALLS_ULTRA',
        NOW(),
        UNIX_TIMESTAMP(NOW()),
        NOW(),
        NOW()
    ),
    (
        (SELECT id FROM vips WHERE level = 1000),
        1000,
        (SELECT id FROM benefit_groups WHERE code = 'SUBTITLE_DIALOGUE_LIMIT'),
        (SELECT id FROM benefits WHERE code = 'SUBTITLE_DIALOGUE_ULTRA'),
        'SUBTITLE_DIALOGUE_ULTRA',
        NOW(),
        UNIX_TIMESTAMP(NOW()),
        NOW(),
        NOW()
    );
