package main

import (
	"fmt"
	"log"
	"loop/internal/config"
	"loop/internal/constants"
	"loop/internal/model"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// 加载配置
	cfg, err := config.NewConfig()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	db, err := connectDB(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 初始化数据
	if err := initAdminData(db); err != nil {
		log.Fatalf("Failed to initialize admin data: %v", err)
	}

	fmt.Println("Admin data initialization completed successfully!")
}

func connectDB(cfg *config.Config) (*gorm.DB, error) {
	// 直接使用配置中的DSN URL
	db, err := gorm.Open(mysql.Open(cfg.DbConfig.Url), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, err
	}

	// 自动迁移表结构
	if err := db.AutoMigrate(model.Models...); err != nil {
		return nil, fmt.Errorf("failed to auto migrate tables: %v", err)
	}

	return db, nil
}

func initAdminData(db *gorm.DB) error {
	// 初始化系统角色
	if err := initSysRoles(db); err != nil {
		return fmt.Errorf("failed to init sys roles: %v", err)
	}

	// 初始化系统用户
	if err := initSysUsers(db); err != nil {
		return fmt.Errorf("failed to init sys users: %v", err)
	}

	// 初始化分类
	if err := initCategories(db); err != nil {
		return fmt.Errorf("failed to init categories: %v", err)
	}

	// 初始化会员等级
	if err := initVips(db); err != nil {
		return fmt.Errorf("failed to init vips: %v", err)
	}

	// 初始化商品
	if err := initTradeProducts(db); err != nil {
		return fmt.Errorf("failed to init trade products: %v", err)
	}

	// 初始化权益组
	if err := initBenefitGroups(db); err != nil {
		return fmt.Errorf("failed to init benefit groups: %v", err)
	}

	// 初始化权益
	if err := initBenefits(db); err != nil {
		return fmt.Errorf("failed to init benefits: %v", err)
	}

	// 初始化会员权益关联
	if err := initVipBenefits(db); err != nil {
		return fmt.Errorf("failed to init vip benefits: %v", err)
	}

	return nil
}

func initSysRoles(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM sys_roles").Error; err != nil {
		return err
	}

	roles := []model.SysRole{
		{
			ModelAutoId: model.ModelAutoId{Id: 1},
			Name:        "admin",
			RoleLevel:   2,
		},
		{
			ModelAutoId: model.ModelAutoId{Id: 2},
			Name:        "normal",
			RoleLevel:   0,
		},
	}

	for _, role := range roles {
		role.CreatedAt = time.Now()
		role.UpdatedAt = time.Now()
		if err := db.Create(&role).Error; err != nil {
			return err
		}
	}

	return nil
}

func initSysUsers(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM sys_users").Error; err != nil {
		return err
	}

	users := []model.SysUser{
		{
			ModelAutoId:    model.ModelAutoId{Id: 1},
			UserName:       "admin",
			PasswordDigest: "$2a$12$Jz8.7h07XTBCIqqE6MFy4uwUuWk7uHkNQMbNCrHpqee.pFcDf1966",
			RoleLevel:      2,
			Status:         0,
		},
	}

	for _, user := range users {
		user.CreatedAt = time.Now()
		user.UpdatedAt = time.Now()
		if err := db.Create(&user).Error; err != nil {
			return err
		}
	}

	return nil
}

func initCategories(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM categories").Error; err != nil {
		return err
	}

	categories := []model.Category{
		// 等级分类 (CategoryTypeId = '1')
		{Name: "零基础", Description: "适合完全没有英语基础的学习者", Priority: 1, CategoryTypeId: "1"},
		{Name: "中级", Description: "适合有一定英语基础的学习者", Priority: 2, CategoryTypeId: "1"},
		{Name: "高级", Description: "适合英语基础较好的学习者", Priority: 3, CategoryTypeId: "1"},

		// 场景分类 (CategoryTypeId = '2')
		{Name: "演讲", Description: "公开演讲和演示技巧", Priority: 1, CategoryTypeId: "2"},
		{Name: "考试", Description: "各类英语考试准备", Priority: 2, CategoryTypeId: "2"},
		{Name: "面试", Description: "求职面试英语技巧", Priority: 3, CategoryTypeId: "2"},
		{Name: "出国", Description: "出国留学和生活英语", Priority: 4, CategoryTypeId: "2"},
		{Name: "写作", Description: "英语写作技巧和练习", Priority: 5, CategoryTypeId: "2"},

		// 主题分类 (CategoryTypeId = '3')
		{Name: "TED", Description: "TED演讲学习资源", Priority: 1, CategoryTypeId: "3"},
		{Name: "YouTube", Description: "YouTube视频学习资源", Priority: 2, CategoryTypeId: "3"},
		{Name: "博客", Description: "英语博客和文章", Priority: 3, CategoryTypeId: "3"},
		{Name: "影视", Description: "电影和电视剧学习资源", Priority: 4, CategoryTypeId: "3"},
		{Name: "书籍", Description: "英语书籍和文学作品", Priority: 5, CategoryTypeId: "3"},
	}

	for _, category := range categories {
		category.CreatedAt = time.Now()
		category.UpdatedAt = time.Now()
		if err := db.Create(&category).Error; err != nil {
			return err
		}
	}

	return nil
}

func initVips(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM vips").Error; err != nil {
		return err
	}

	vips := []model.VIP{
		{
			ModelAutoId: model.ModelAutoId{Id: 1},
			Name:        "普通会员",
			Level:       1,
		},
		{
			ModelAutoId: model.ModelAutoId{Id: 2},
			Name:        "PRO会员",
			Level:       100,
		},
		{
			ModelAutoId: model.ModelAutoId{Id: 3},
			Name:        "ULTRA会员",
			Level:       1000,
		},
	}

	for _, vip := range vips {
		vip.CreatedAt = time.Now()
		vip.UpdatedAt = time.Now()
		if err := db.Create(&vip).Error; err != nil {
			return err
		}
	}

	return nil
}

func initTradeProducts(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM trade_products").Error; err != nil {
		return err
	}

	products := []model.TradeProduct{
		// Android端商品
		{
			ModelAutoId:    model.ModelAutoId{Id: 1},
			Name:           "月卡",
			Type:           1,
			IsSubscription: 1,
			Price:          19.9,
			OriginPrice:    39.9,
			IosProductId:   "",
			Currency:       "RMB",
			Terminal:       1,
			VipID:          1,
			Days:           31,
		},
		{
			ModelAutoId:    model.ModelAutoId{Id: 2},
			Name:           "年卡",
			Type:           1,
			IsSubscription: 1,
			Price:          119.9,
			OriginPrice:    339.9,
			IosProductId:   "",
			Currency:       "RMB",
			Terminal:       1,
			VipID:          2,
			Days:           365,
		},
		{
			ModelAutoId:    model.ModelAutoId{Id: 3},
			Name:           "终身会员",
			Type:           1,
			IsSubscription: 0,
			Price:          1199.9,
			OriginPrice:    3399.9,
			IosProductId:   "",
			Currency:       "RMB",
			Terminal:       1,
			VipID:          3,
			Days:           -1,
		},
		// iOS端商品
		{
			ModelAutoId:    model.ModelAutoId{Id: 4},
			Name:           "月卡",
			Type:           1,
			IsSubscription: 1,
			Price:          38,
			OriginPrice:    39.9,
			IosProductId:   "11",
			Currency:       "RMB",
			Terminal:       2,
			VipID:          1,
			Days:           31,
		},
		{
			ModelAutoId:    model.ModelAutoId{Id: 5},
			Name:           "年卡",
			Type:           1,
			IsSubscription: 1,
			Price:          198,
			OriginPrice:    339.9,
			IosProductId:   "12",
			Currency:       "RMB",
			Terminal:       2,
			VipID:          2,
			Days:           365,
		},
		{
			ModelAutoId:    model.ModelAutoId{Id: 6},
			Name:           "终身",
			Type:           1,
			IsSubscription: 0,
			Price:          198,
			OriginPrice:    3399.9,
			IosProductId:   "12",
			Currency:       "RMB",
			Terminal:       2,
			VipID:          3,
			Days:           -1,
		},
	}

	for _, product := range products {
		product.CreatedAt = time.Now()
		product.UpdatedAt = time.Now()
		if err := db.Create(&product).Error; err != nil {
			return err
		}
	}

	return nil
}

func initBenefitGroups(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM benefit_groups").Error; err != nil {
		return err
	}

	groups := []model.BenefitGroup{
		{
			Name:        "视频大小限制组",
			Code:        "VIDEO_SIZE_LIMIT",
			Status:      1,
			Description: "视频大小限制权益组，包含不同等级的限制",
		},
		{
			Name:        "AI调用次数组",
			Code:        "AI_CALLS_LIMIT",
			Status:      1,
			Description: "AI调用次数权益组，包含不同等级的限制",
		},
		{
			Name:        "字幕对话次数组",
			Code:        "SUBTITLE_DIALOGUE_LIMIT",
			Status:      1,
			Description: "字幕对话次数权益组，包含不同等级的限制",
		},
	}

	for _, group := range groups {
		group.CreatedAt = time.Now()
		group.UpdatedAt = time.Now()
		if err := db.Create(&group).Error; err != nil {
			return err
		}
	}

	return nil
}

func initBenefits(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM benefits").Error; err != nil {
		return err
	}

	// 获取权益组ID
	var videoGroup, aiGroup, subtitleGroup model.BenefitGroup
	if err := db.Where("code = ?", "VIDEO_SIZE_LIMIT").First(&videoGroup).Error; err != nil {
		return err
	}
	if err := db.Where("code = ?", "AI_CALLS_LIMIT").First(&aiGroup).Error; err != nil {
		return err
	}
	if err := db.Where("code = ?", "SUBTITLE_DIALOGUE_LIMIT").First(&subtitleGroup).Error; err != nil {
		return err
	}

	benefits := []model.Benefit{
		// 视频大小限制组下的权益
		{
			Name:             "普通会员视频限制",
			Code:             constants.BenefitCode("UPLOAD_LIMIT_BASIC"),
			Level:            10,
			CycleType:        6, // 无周期
			CycleCount:       1,
			BenefitCount:     500, // 500MB
			BenefitGroupID:   videoGroup.Id,
			BenefitGroupName: videoGroup.Name,
			BenefitGroupCode: videoGroup.Code,
			Description:      "普通会员单视频最大500MB",
			Status:           1,
		},
		{
			Name:             "Pro会员视频限制",
			Code:             constants.BenefitCode("UPLOAD_LIMIT_PRO"),
			Level:            20,
			CycleType:        6, // 无周期
			CycleCount:       100,
			BenefitCount:     2048, // 2GB
			BenefitGroupID:   videoGroup.Id,
			BenefitGroupName: videoGroup.Name,
			BenefitGroupCode: videoGroup.Code,
			Description:      "Pro会员单视频最大2GB",
			Status:           1,
		},
		{
			Name:             "Ultra会员视频限制",
			Code:             constants.BenefitCode("UPLOAD_LIMIT_ULTRA"),
			Level:            30,
			CycleType:        6, // 无周期
			CycleCount:       1000,
			BenefitCount:     5120, // 5GB
			BenefitGroupID:   videoGroup.Id,
			BenefitGroupName: videoGroup.Name,
			BenefitGroupCode: videoGroup.Code,
			Description:      "Ultra会员单视频最大5GB",
			Status:           1,
		},
		// AI调用次数组下的权益
		{
			Name:             "普通会员AI调用次数",
			Code:             constants.BenefitCode("AI_CALLS_BASIC"),
			Level:            10,
			CycleType:        3, // 月周期
			CycleCount:       1,
			BenefitCount:     50, // 每月50次
			BenefitGroupID:   aiGroup.Id,
			BenefitGroupName: aiGroup.Name,
			BenefitGroupCode: aiGroup.Code,
			Description:      "普通会员每月50次AI调用",
			Status:           1,
		},
		{
			Name:             "Pro会员AI调用次数",
			Code:             constants.BenefitCode("AI_CALLS_PRO"),
			Level:            20,
			CycleType:        3, // 月周期
			CycleCount:       100,
			BenefitCount:     200, // 每月200次
			BenefitGroupID:   aiGroup.Id,
			BenefitGroupName: aiGroup.Name,
			BenefitGroupCode: aiGroup.Code,
			Description:      "Pro会员每月200次AI调用",
			Status:           1,
		},
		{
			Name:             "Ultra会员AI调用次数",
			Code:             constants.BenefitCode("AI_CALLS_ULTRA"),
			Level:            30,
			CycleType:        3, // 月周期
			CycleCount:       1000,
			BenefitCount:     1000, // 每月1000次
			BenefitGroupID:   aiGroup.Id,
			BenefitGroupName: aiGroup.Name,
			BenefitGroupCode: aiGroup.Code,
			Description:      "Ultra会员每月1000次AI调用",
			Status:           1,
		},
	}

	// 添加字幕对话次数组下的权益
	subtitleBenefits := []model.Benefit{
		{
			Name:             "普通会员字幕对话次数",
			Code:             constants.BenefitCode("SUBTITLE_DIALOGUE_BASIC"),
			Level:            10,
			CycleType:        3, // 月周期
			CycleCount:       1,
			BenefitCount:     100, // 每月100次
			BenefitGroupID:   subtitleGroup.Id,
			BenefitGroupName: subtitleGroup.Name,
			BenefitGroupCode: subtitleGroup.Code,
			Description:      "普通会员每月100次字幕对话",
			Status:           1,
		},
		{
			Name:             "Pro会员字幕对话次数",
			Code:             constants.BenefitCode("SUBTITLE_DIALOGUE_PRO"),
			Level:            20,
			CycleType:        3, // 月周期
			CycleCount:       100,
			BenefitCount:     500, // 每月500次
			BenefitGroupID:   subtitleGroup.Id,
			BenefitGroupName: subtitleGroup.Name,
			BenefitGroupCode: subtitleGroup.Code,
			Description:      "Pro会员每月500次字幕对话",
			Status:           1,
		},
		{
			Name:             "Ultra会员字幕对话次数",
			Code:             constants.BenefitCode("SUBTITLE_DIALOGUE_ULTRA"),
			Level:            30,
			CycleType:        3, // 月周期
			CycleCount:       1000,
			BenefitCount:     2000, // 每月2000次
			BenefitGroupID:   subtitleGroup.Id,
			BenefitGroupName: subtitleGroup.Name,
			BenefitGroupCode: subtitleGroup.Code,
			Description:      "Ultra会员每月2000次字幕对话",
			Status:           1,
		},
	}

	// 合并所有权益
	benefits = append(benefits, subtitleBenefits...)

	for _, benefit := range benefits {
		benefit.CreatedAt = time.Now()
		benefit.UpdatedAt = time.Now()
		if err := db.Create(&benefit).Error; err != nil {
			return err
		}
	}

	return nil
}

func initVipBenefits(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM vip_benefits").Error; err != nil {
		return err
	}

	// 获取VIP信息
	var normalVip, proVip, ultraVip model.VIP
	if err := db.Where("level = ?", 1).First(&normalVip).Error; err != nil {
		return err
	}
	if err := db.Where("level = ?", 100).First(&proVip).Error; err != nil {
		return err
	}
	if err := db.Where("level = ?", 1000).First(&ultraVip).Error; err != nil {
		return err
	}

	// 获取权益组信息
	var videoGroup, aiGroup, subtitleGroup model.BenefitGroup
	if err := db.Where("code = ?", "VIDEO_SIZE_LIMIT").First(&videoGroup).Error; err != nil {
		return err
	}
	if err := db.Where("code = ?", "AI_CALLS_LIMIT").First(&aiGroup).Error; err != nil {
		return err
	}
	if err := db.Where("code = ?", "SUBTITLE_DIALOGUE_LIMIT").First(&subtitleGroup).Error; err != nil {
		return err
	}

	// 获取权益信息
	var basicUpload, proUpload, ultraUpload model.Benefit
	var basicAI, proAI, ultraAI model.Benefit
	var basicSubtitle, proSubtitle, ultraSubtitle model.Benefit

	if err := db.Where("code = ?", "UPLOAD_LIMIT_BASIC").First(&basicUpload).Error; err != nil {
		return err
	}
	if err := db.Where("code = ?", "UPLOAD_LIMIT_PRO").First(&proUpload).Error; err != nil {
		return err
	}
	if err := db.Where("code = ?", "UPLOAD_LIMIT_ULTRA").First(&ultraUpload).Error; err != nil {
		return err
	}
	if err := db.Where("code = ?", "AI_CALLS_BASIC").First(&basicAI).Error; err != nil {
		return err
	}
	if err := db.Where("code = ?", "AI_CALLS_PRO").First(&proAI).Error; err != nil {
		return err
	}
	if err := db.Where("code = ?", "AI_CALLS_ULTRA").First(&ultraAI).Error; err != nil {
		return err
	}
	if err := db.Where("code = ?", "SUBTITLE_DIALOGUE_BASIC").First(&basicSubtitle).Error; err != nil {
		return err
	}
	if err := db.Where("code = ?", "SUBTITLE_DIALOGUE_PRO").First(&proSubtitle).Error; err != nil {
		return err
	}
	if err := db.Where("code = ?", "SUBTITLE_DIALOGUE_ULTRA").First(&ultraSubtitle).Error; err != nil {
		return err
	}

	now := time.Now()
	vipBenefits := []model.VipBenefit{
		// 普通会员(level=1)的权益
		{
			VipID:           normalVip.Id,
			VipLevel:        uint(normalVip.Level),
			BenefitGroupID:  videoGroup.Id,
			BenefitID:       basicUpload.Id,
			BenefitCode:     basicUpload.Code,
			CreateTime:      now,
			CreateTimestamp: now.Unix(),
		},
		{
			VipID:           normalVip.Id,
			VipLevel:        uint(normalVip.Level),
			BenefitGroupID:  aiGroup.Id,
			BenefitID:       basicAI.Id,
			BenefitCode:     basicAI.Code,
			CreateTime:      now,
			CreateTimestamp: now.Unix(),
		},
		{
			VipID:           normalVip.Id,
			VipLevel:        uint(normalVip.Level),
			BenefitGroupID:  subtitleGroup.Id,
			BenefitID:       basicSubtitle.Id,
			BenefitCode:     basicSubtitle.Code,
			CreateTime:      now,
			CreateTimestamp: now.Unix(),
		},
		// PRO会员(level=100)的权益
		{
			VipID:           proVip.Id,
			VipLevel:        uint(proVip.Level),
			BenefitGroupID:  videoGroup.Id,
			BenefitID:       proUpload.Id,
			BenefitCode:     proUpload.Code,
			CreateTime:      now,
			CreateTimestamp: now.Unix(),
		},
		{
			VipID:           proVip.Id,
			VipLevel:        uint(proVip.Level),
			BenefitGroupID:  aiGroup.Id,
			BenefitID:       proAI.Id,
			BenefitCode:     proAI.Code,
			CreateTime:      now,
			CreateTimestamp: now.Unix(),
		},
		{
			VipID:           proVip.Id,
			VipLevel:        uint(proVip.Level),
			BenefitGroupID:  subtitleGroup.Id,
			BenefitID:       proSubtitle.Id,
			BenefitCode:     proSubtitle.Code,
			CreateTime:      now,
			CreateTimestamp: now.Unix(),
		},
		// ULTRA会员(level=1000)的权益
		{
			VipID:           ultraVip.Id,
			VipLevel:        uint(ultraVip.Level),
			BenefitGroupID:  videoGroup.Id,
			BenefitID:       ultraUpload.Id,
			BenefitCode:     ultraUpload.Code,
			CreateTime:      now,
			CreateTimestamp: now.Unix(),
		},
		{
			VipID:           ultraVip.Id,
			VipLevel:        uint(ultraVip.Level),
			BenefitGroupID:  aiGroup.Id,
			BenefitID:       ultraAI.Id,
			BenefitCode:     ultraAI.Code,
			CreateTime:      now,
			CreateTimestamp: now.Unix(),
		},
		{
			VipID:           ultraVip.Id,
			VipLevel:        uint(ultraVip.Level),
			BenefitGroupID:  subtitleGroup.Id,
			BenefitID:       ultraSubtitle.Id,
			BenefitCode:     ultraSubtitle.Code,
			CreateTime:      now,
			CreateTimestamp: now.Unix(),
		},
	}

	for _, vipBenefit := range vipBenefits {
		vipBenefit.CreatedAt = time.Now()
		vipBenefit.UpdatedAt = time.Now()
		if err := db.Create(&vipBenefit).Error; err != nil {
			return err
		}
	}

	return nil
}
