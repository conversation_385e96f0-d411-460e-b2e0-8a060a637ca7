package main

import (
	"fmt"
	"log"
	"os"

	"gopkg.in/yaml.v2"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"loop/internal/config"
	"loop/internal/model"
)

// loadConfig 加载配置文件，自动检测路径
func loadConfig() (*config.Config, error) {
	// 尝试不同的配置文件路径
	configPaths := []string{
		"configs/config.yaml",    // 从项目根目录运行
		"../configs/config.yaml", // 从scripts目录运行
	}

	var configFile string
	for _, path := range configPaths {
		if _, err := os.Stat(path); err == nil {
			configFile = path
			break
		}
	}

	if configFile == "" {
		return nil, fmt.Errorf("config file not found in any of the expected paths")
	}

	cfg := &config.Config{}
	yamlFile, err := os.ReadFile(configFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file %s: %v", configFile, err)
	}

	if err = yaml.Unmarshal(yamlFile, cfg); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %v", err)
	}

	fmt.Printf("配置文件加载成功: %s\n", configFile)
	return cfg, nil
}

func main() {
	fmt.Println("开始初始化管理员数据...")

	// 加载配置
	cfg, err := loadConfig()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	fmt.Println("正在连接数据库...")
	db, err := connectDB(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	fmt.Println("数据库连接成功")

	// 初始化数据
	if err := initAdminData(db); err != nil {
		log.Fatalf("Failed to initialize admin data: %v", err)
	}

	fmt.Println("Admin data initialization completed successfully!")

	// 验证数据
	fmt.Println("\n=== 开始数据验证 ===")
	if err := verifyData(db); err != nil {
		log.Fatalf("Data verification failed: %v", err)
	}
	fmt.Println("=== 数据验证完成 ===")
}

func connectDB(cfg *config.Config) (*gorm.DB, error) {
	// 直接使用配置中的DSN URL
	db, err := gorm.Open(mysql.Open(cfg.DbConfig.Url), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, err
	}

	// 自动迁移表结构
	if err := db.AutoMigrate(model.Models...); err != nil {
		return nil, fmt.Errorf("failed to auto migrate tables: %v", err)
	}

	return db, nil
}

func initAdminData(db *gorm.DB) error {
	// 初始化系统角色
	if err := initSysRoles(db); err != nil {
		return fmt.Errorf("failed to init sys roles: %v", err)
	}

	// 初始化系统用户
	if err := initSysUsers(db); err != nil {
		return fmt.Errorf("failed to init sys users: %v", err)
	}

	// 初始化分类
	if err := initCategories(db); err != nil {
		return fmt.Errorf("failed to init categories: %v", err)
	}

	// 初始化VIP等级
	if err := initVIPs(db); err != nil {
		return fmt.Errorf("failed to init VIPs: %v", err)
	}

	// 初始化商品
	if err := initTradeProducts(db); err != nil {
		return fmt.Errorf("failed to init trade products: %v", err)
	}

	// 初始化权益组
	if err := initBenefitGroups(db); err != nil {
		return fmt.Errorf("failed to init benefit groups: %v", err)
	}

	// 初始化权益
	if err := initBenefits(db); err != nil {
		return fmt.Errorf("failed to init benefits: %v", err)
	}

	// 初始化VIP权益关联
	if err := initVipBenefits(db); err != nil {
		return fmt.Errorf("failed to init vip benefits: %v", err)
	}

	return nil
}
