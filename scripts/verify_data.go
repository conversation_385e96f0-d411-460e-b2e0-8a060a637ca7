package main

import (
	"fmt"
	"log"
	"os"

	"gopkg.in/yaml.v2"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"loop/internal/config"
	"loop/internal/model"
)

func loadConfig() (*config.Config, error) {
	configFile := "configs/config.yaml"
	cfg := &config.Config{}

	yamlFile, err := os.ReadFile(configFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %v", err)
	}

	if err = yaml.Unmarshal(yamlFile, cfg); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %v", err)
	}

	return cfg, nil
}

func main() {
	// 加载配置
	cfg, err := loadConfig()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	db, err := gorm.Open(mysql.Open(cfg.DbConfig.Url), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // 静默模式，减少输出
	})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	fmt.Println("=== 数据验证报告 ===")

	// 验证系统角色
	var roleCount int64
	db.Model(&model.SysRole{}).Count(&roleCount)
	fmt.Printf("系统角色数量: %d\n", roleCount)

	// 验证系统用户
	var userCount int64
	db.Model(&model.SysUser{}).Count(&userCount)
	fmt.Printf("系统用户数量: %d\n", userCount)

	// 验证分类
	var categoryCount int64
	db.Model(&model.Category{}).Count(&categoryCount)
	fmt.Printf("分类数量: %d\n", categoryCount)

	// 验证会员等级
	var vipCount int64
	db.Model(&model.VIP{}).Count(&vipCount)
	fmt.Printf("会员等级数量: %d\n", vipCount)

	// 验证商品
	var productCount int64
	db.Model(&model.TradeProduct{}).Count(&productCount)
	fmt.Printf("商品数量: %d\n", productCount)

	// 验证权益组
	var benefitGroupCount int64
	db.Model(&model.BenefitGroup{}).Count(&benefitGroupCount)
	fmt.Printf("权益组数量: %d\n", benefitGroupCount)

	// 验证权益
	var benefitCount int64
	db.Model(&model.Benefit{}).Count(&benefitCount)
	fmt.Printf("权益数量: %d\n", benefitCount)

	// 验证会员权益关联
	var vipBenefitCount int64
	db.Model(&model.VipBenefit{}).Count(&vipBenefitCount)
	fmt.Printf("会员权益关联数量: %d\n", vipBenefitCount)

	fmt.Println("\n=== 详细数据检查 ===")

	// 检查系统角色详情
	var roles []model.SysRole
	db.Find(&roles)
	fmt.Println("\n系统角色:")
	for _, role := range roles {
		fmt.Printf("  - ID: %d, 名称: %s, 等级: %d\n", role.Id, role.Name, role.RoleLevel)
	}

	// 检查会员等级详情
	var vips []model.VIP
	db.Find(&vips)
	fmt.Println("\n会员等级:")
	for _, vip := range vips {
		fmt.Printf("  - ID: %d, 名称: %s, 等级: %d\n", vip.Id, vip.Name, vip.Level)
	}

	// 检查权益组详情
	var benefitGroups []model.BenefitGroup
	db.Find(&benefitGroups)
	fmt.Println("\n权益组:")
	for _, group := range benefitGroups {
		fmt.Printf("  - ID: %d, 名称: %s, 代码: %s\n", group.Id, group.Name, group.Code)
	}

	// 检查分类详情（按类型分组）
	var categories []model.Category
	db.Find(&categories)
	fmt.Println("\n分类:")
	categoryTypes := make(map[string][]model.Category)
	for _, cat := range categories {
		categoryTypes[cat.CategoryTypeId] = append(categoryTypes[cat.CategoryTypeId], cat)
	}

	typeNames := map[string]string{
		"1": "等级分类",
		"2": "场景分类",
		"3": "主题分类",
	}

	for typeID, cats := range categoryTypes {
		fmt.Printf("  %s (类型ID: %s):\n", typeNames[typeID], typeID)
		for _, cat := range cats {
			fmt.Printf("    - %s (优先级: %d)\n", cat.Name, cat.Priority)
		}
	}

	fmt.Println("\n=== 验证完成 ===")
	fmt.Println("所有数据已成功初始化！")
}
