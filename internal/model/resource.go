package model

import (
	"encoding/json"
	"errors"
	"loop/internal/config"
	"loop/internal/constants"
	"loop/internal/response"
	"loop/pkg/enum"
	"strconv"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"go4.org/syncutil/singleflight"
)

// Category 具体分类（二级分类）
type Category struct {
	Model
	Name           string `gorm:"type:varchar(100);not null"` // 分类名称，如"初级"、"中级"、"演讲"
	Description    string `gorm:"type:text"`                  // 描述
	Priority       int64  `gorm:"type:bigint;default:0"`      // 排序优先级
	CategoryTypeId string `gorm:"type:varchar(20);not null"`  // 关联的分类类型ID
}

func (Category) TableName() string {
	return "categories"
}

// Series 表示剧集，包含多个资源（视频）
type Series struct {
	Model
	Cover    string `gorm:"type:text" json:"cover" form:"cover"`
	Priority int64  `gorm:"type:bigint;default:0"`
}

func (Series) TableName() string {
	return "series"
}

// 多语言信息表 包含标题、描述、字幕
type SeriesRelation struct {
	ModelAutoId
	SeriesId    string `gorm:"type:varchar(20);not null"`
	LangCode    string `gorm:"size:128;not null;comment:对应的母语列表的ID"`
	Title       string `gorm:"type:text"`
	Description string `gorm:"type:text"`
	Statement   string `gorm:"type:text" json:"statement" form:"statement"`
}

func (SeriesRelation) TableName() string {
	return "series_relations"
}

// FeaturedContent 代表精选的内容项，可以是剧集、视频等
type FeaturedContent struct {
	ModelAutoId
	ContentID   string `gorm:"type:varchar(20);not null"`
	ContentType int    `gorm:"not null"` // 内容的类型（如: 'series', 'video'）
	LangCode    string `gorm:"size:128;not null;comment:对应的母语列表的ID"`
}

func (FeaturedContent) TableName() string {
	return "featured_contents"
}

// 资源
type Resource struct {
	Model
	VideoURL              string `gorm:"type:text"`                           // 视频URL
	Cover                 string `gorm:"type:text"`                           // 封面图URL
	Duration              int64  `gorm:"size:128;"`                           // 秒
	Author                string `gorm:"size:128;"`                           // 作者
	Priority              int64  `gorm:"type:bigint;default:0"`               // 优先级
	OriginReourcelationId uint   `gorm:"type:bigint;default:0"`               // 原始资源关系ID
	Tags                  string `gorm:"type:text;comment:资源标签，JSON格式的字符串数组"` // 标签，JSON格式的字符串数组
}

func (Resource) TableName() string {
	return "resources"
}

// 多语言信息表 包含标题、描述、字幕
type ResourceRelation struct {
	ModelAutoId
	ResourceId  string `gorm:"not null"`
	LangCode    string `gorm:"size:128;not null;comment:对应的母语列表的ID"`
	Title       string `gorm:"type:text"`
	Description string `gorm:"type:text"`
	SubtitleUrl string `gorm:"type:text"`
}

func (ResourceRelation) TableName() string {
	return "resource_relations"
}

// 剧集与分类的关联表
type CategorySeriesRelations struct {
	ModelAutoId
	SeriesId   string `gorm:"type:varchar(20);not null"`
	CategoryId string `gorm:"type:varchar(20);not null;comment:关联的分类ID"`
}

func (CategorySeriesRelations) TableName() string {
	return "category_series_relations"
}

// 分类和视频的关联表
type CategoryResourceRelations struct {
	ModelAutoId
	ResourceId string `gorm:"not null"`
	CategoryId string `gorm:"type:varchar(20);not null;comment:关联的分类ID"`
}

func (CategoryResourceRelations) TableName() string {
	return "category_resource_relations"
}

// 剧集和视频的关联表
type SeriesResourceRelations struct {
	ModelAutoId
	ResourceId string `gorm:"not null"`
	SeriesId   string `gorm:"type:varchar(20);not null"`
}

func (SeriesResourceRelations) TableName() string {
	return "series_resource_relations"
}

func NewResourceModel(dbModel *DbModel, config *config.Config) *ResourceModel {
	return &ResourceModel{
		DbModel: dbModel,
		config:  config,
		sg:      &singleflight.Group{}}
}

type ResourceModel struct {
	*DbModel
	config *config.Config
	sg     *singleflight.Group
}

func (r *ResourceModel) GetById(id uint) (*Resource, error) {
	// 将uint ID转换为string
	idString := strconv.FormatUint(uint64(id), 10)
	return r.GetByIdString(idString)
}

// GetByIdString 通过string类型的ID获取资源（用于API层）
func (r *ResourceModel) GetByIdString(id string) (*Resource, error) {
	query := Resource{Model: Model{Id: id}}
	result := Resource{}
	if found, err := r.GetOne(&result, query); !found {
		return nil, err
	}
	return &result, nil
}

func (r *ResourceModel) GetByIds(ids []string) ([]*Resource, error) {
	result := []*Resource{}
	err := r.GetList(&result, "id IN ?", ids)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// GetResourcesAndRelations 联合查询资源和资源关系
// ids: 资源ID列表
// langCode: 语言代码
// 返回资源列表和资源关系列表
func (r *ResourceModel) GetResourcesAndRelations(ids []string, langCode string) ([]*Resource, []*ResourceRelation, error) {
	// 1. 获取所有资源
	resources, err := r.GetByIds(ids)
	if err != nil {
		return nil, nil, err
	}

	// 如果没有找到资源，直接返回空列表
	if len(resources) == 0 {
		return []*Resource{}, []*ResourceRelation{}, nil
	}

	// 2. 获取所有资源关系
	var relations []*ResourceRelation
	err = r.GetList(&relations, "resource_id IN ? AND lang_code = ?", ids, langCode)
	if err != nil {
		return nil, nil, err
	}

	return resources, relations, nil
}

func (r *ResourceModel) GetFeatureContent(targetLang string) ([]response.SeriesWithLang, []*Resource, error) {
	var featuredContents []*FeaturedContent
	err := r.GetList(&featuredContents, FeaturedContent{LangCode: targetLang})
	if err != nil {
		return nil, nil, err
	}

	featuredContentIds := make(map[int][]string)
	for _, content := range featuredContents {
		featuredContentIds[content.ContentType] = append(featuredContentIds[content.ContentType], content.ContentID)
	}
	var seriesFeaturedContentIds []string
	var resourceFeaturedContentIds []string

	for _, content := range featuredContents {
		if content.ContentType == int(enum.Series) {
			seriesFeaturedContentIds = append(seriesFeaturedContentIds, content.ContentID)
		} else if content.ContentType == int(enum.Resource) {
			resourceFeaturedContentIds = append(resourceFeaturedContentIds, content.ContentID)
		}
	}
	var serieses []*Series
	if len(seriesFeaturedContentIds) != 0 {
		err = r.GetList(&serieses, "id IN ?", seriesFeaturedContentIds)
		if err != nil {
			return nil, nil, err
		}
	}

	// 获取SeriesRelation数据
	var seriesRelations []*SeriesRelation
	if len(seriesFeaturedContentIds) != 0 && targetLang != "" {
		err = r.GetList(&seriesRelations, "series_id IN ? AND lang_code = ?", seriesFeaturedContentIds, targetLang)
		if err != nil {
			return nil, nil, err
		}
	}

	// 构建SeriesRelation的映射
	seriesRelMap := make(map[string]*SeriesRelation)
	for _, rel := range seriesRelations {
		seriesRelMap[rel.SeriesId] = rel
	}

	// 转换为SeriesWithLang
	var seriesWithLangs []response.SeriesWithLang
	for _, s := range serieses {
		swl := response.SeriesWithLang{
			Id:          s.Id,
			Cover:       s.Cover,
			Priority:    s.Priority,
			Title:       "",
			Description: "",
		}
		if rel, ok := seriesRelMap[s.Id]; ok {
			swl.Title = rel.Title
			swl.Description = rel.Description
		}
		seriesWithLangs = append(seriesWithLangs, swl)
	}

	var resources []*Resource
	if len(resourceFeaturedContentIds) != 0 {
		err = r.GetList(&resources, "id IN ?", resourceFeaturedContentIds)
		if err != nil {
			return nil, nil, err
		}
	}

	return seriesWithLangs, resources, nil
}

func (s *ResourceModel) GetFeatureContentIds() ([]string, error) {
	// 因为精选的比较少 可以直接查出所有的精选 然后再到列表里去匹配
	var featuredContents []FeaturedContent
	err := s.GetList(&featuredContents, FeaturedContent{ContentType: int(enum.Resource)})
	if err != nil {
		return nil, err
	}
	featuredContentIds := make([]string, len(featuredContents))
	for idx, series := range featuredContents {
		featuredContentIds[idx] = series.ContentID
	}
	return featuredContentIds, nil
}

// 如果视频没有任何的分类  即使选择全部也不会出现 因为他可能在剧集里
func (r *ResourceModel) GetResouceSeriesByCategory(targetLangCode string, categoryIds []string) ([]response.SeriesWithLang, []*response.ResourceWithLang, error) {
	var err error
	var seriesIds []string
	if len(categoryIds) > 0 {
		subQuery := r.Table("category_series_relations").
			Select("series_id").
			Where("category_id IN ?", categoryIds).
			Group("series_id")
		err = subQuery.Having("COUNT(DISTINCT category_id) = ?", len(categoryIds)).
			Scan(&seriesIds).Error
		if err != nil {
			return nil, nil, err
		}
	} else {
		err = r.Table("category_series_relations").
			Select("series_id").
			Group("series_id").
			Scan(&seriesIds).Error
		if err != nil {
			return nil, nil, err
		}
	}

	// 先筛选出有targetLangCode的seriesId
	var filteredSeriesIds []string
	if len(seriesIds) > 0 && targetLangCode != "" {
		err = r.Table("series_relations").
			Select("series_id").
			Where("series_id IN ? AND lang_code = ?", seriesIds, targetLangCode).
			Group("series_id").
			Scan(&filteredSeriesIds).Error
		if err != nil {
			return nil, nil, err
		}
	} else {
		filteredSeriesIds = seriesIds
	}

	var serieses []*Series
	if len(filteredSeriesIds) > 0 {
		err = r.GetList(&serieses, "id IN ?", filteredSeriesIds)
		if err != nil {
			return nil, nil, err
		}
	}

	// 查出所有SeriesRelation
	var seriesRelations []*SeriesRelation
	if len(filteredSeriesIds) > 0 && targetLangCode != "" {
		err = r.GetList(&seriesRelations, "series_id IN ? AND lang_code = ?", filteredSeriesIds, targetLangCode)
		if err != nil {
			return nil, nil, err
		}
	}
	// 构建map
	seriesRelMap := make(map[string]*SeriesRelation)
	for _, rel := range seriesRelations {
		seriesRelMap[rel.SeriesId] = rel
	}
	var seriesWithLangs []response.SeriesWithLang
	for _, s := range serieses {
		swl := response.SeriesWithLang{
			Id:          s.Id,
			Cover:       s.Cover,
			Priority:    s.Priority,
			Title:       "",
			Description: "",
		}
		if rel, ok := seriesRelMap[s.Id]; ok {
			swl.Title = rel.Title
			swl.Description = rel.Description
		}
		seriesWithLangs = append(seriesWithLangs, swl)
	}

	var resourceIds []string
	if len(categoryIds) > 0 {
		subQuery := r.Table("category_resource_relations").
			Select("resource_id").
			Where("category_id IN ?", categoryIds).
			Group("resource_id")
		err = subQuery.Having("COUNT(DISTINCT category_id) = ?", len(categoryIds)).
			Scan(&resourceIds).Error
		if err != nil {
			return nil, nil, err
		}
	} else {
		err = r.Table("category_resource_relations").
			Select("resource_id").
			Group("resource_id").
			Scan(&resourceIds).Error
		if err != nil {
			return nil, nil, err
		}
	}

	// 过滤掉属于剧集的资源
	var resourceInSeriesIds []string
	if len(resourceIds) > 0 {
		r.Table("series_resource_relations").
			Select("resource_id").
			Where("resource_id IN ?", resourceIds).
			Group("resource_id").
			Scan(&resourceInSeriesIds)
	}
	resourceIdSet := make(map[string]struct{}, len(resourceIds))
	for _, id := range resourceIds {
		resourceIdSet[id] = struct{}{}
	}
	for _, id := range resourceInSeriesIds {
		delete(resourceIdSet, id)
	}
	var filteredResourceIds []string
	for id := range resourceIdSet {
		filteredResourceIds = append(filteredResourceIds, id)
	}

	// 只保留有 targetLangCode 资源关系的资源
	var resourceRelations []*ResourceRelation
	if len(filteredResourceIds) > 0 {
		err = r.GetList(&resourceRelations, "resource_id IN ? AND lang_code = ?", filteredResourceIds, targetLangCode)
		if err != nil {
			return nil, nil, err
		}
	}
	// 构建map
	relationMap := make(map[string]*ResourceRelation)
	for _, rel := range resourceRelations {
		relationMap[rel.ResourceId] = rel
	}
	var finalResourceIds []string
	for _, rel := range resourceRelations {
		finalResourceIds = append(finalResourceIds, rel.ResourceId)
	}
	var resources []*Resource
	if len(finalResourceIds) > 0 {
		resources, err = r.GetByIds(finalResourceIds)
		if err != nil {
			return nil, nil, err
		}
	}
	// 组装ResourceWithLang
	var resourcesWithLang []*response.ResourceWithLang
	for _, rsc := range resources {
		rel := relationMap[rsc.Id]
		resourcesWithLang = append(resourcesWithLang, &response.ResourceWithLang{
			Id:          rsc.Id,
			Cover:       rsc.Cover,
			ContentType: int(enum.Resource),
			Title:       rel.Title,
			VideoURL:    rsc.VideoURL,
			Duration:    rsc.Duration,
			Author:      rsc.Author,
		})
	}
	return seriesWithLangs, resourcesWithLang, nil
}

func (r *ResourceModel) GetOriginResourceRelation(resourceID string) (*ResourceRelation, error) {
	resourceResult, err := r.GetByIdString(resourceID)
	if err != nil {
		return nil, err
	}
	if resourceResult == nil {
		return nil, nil
	}
	var result ResourceRelation
	found, err := r.GetOne(&result, ResourceRelation{ModelAutoId: ModelAutoId{Id: resourceResult.OriginReourcelationId}})
	if err != nil {
		return nil, err
	}
	if !found {
		return nil, nil
	}
	return &result, nil
}

func (r *ResourceModel) GetDefalutResourceRelationByLang(resourceID string, langCode string) (*ResourceRelation, error) {
	resourceResult, err := r.GetByIdString(resourceID)
	if err != nil {
		return nil, err
	}
	if resourceResult == nil {
		return nil, nil
	}

	var resourceRelations []*ResourceRelation
	err = r.GetList(&resourceRelations, ResourceRelation{ResourceId: resourceID})
	if err != nil {
		return nil, err
	}

	//先通过LangCode找，找不到说明没有配置对应语言的
	for _, rr := range resourceRelations {
		if rr.LangCode == langCode {
			return rr, nil
		}
	}

	//然后根据OriginReourcelationId找 找不到说明程序本身有问题了
	for _, rr := range resourceRelations {
		if rr.Id == resourceResult.OriginReourcelationId {
			return rr, nil
		}
	}

	var result ResourceRelation
	found, err := r.GetOne(&result, ResourceRelation{ModelAutoId: ModelAutoId{Id: resourceResult.OriginReourcelationId}})
	if err != nil {
		return nil, err
	}
	if !found {
		return nil, nil
	}
	return &result, nil
}

// 获取视频本身的字幕以及用户自己
func (r *ResourceModel) GetOriginAndNativeLangResourceRelation(resourceID string, langCode string) (*ResourceRelation, *ResourceRelation, error) {
	var originResourceRelation *ResourceRelation
	var nativeLangResourceRelation *ResourceRelation

	resourceResult, err := r.GetByIdString(resourceID)
	if err != nil {
		return nil, nil, err
	}
	if resourceResult == nil {
		return nil, nil, nil
	}

	var resourceRelations []*ResourceRelation
	err = r.GetList(&resourceRelations, ResourceRelation{ResourceId: resourceID})
	if err != nil {
		return nil, nil, err
	}
	logrus.Info("GetOriginAndNativeLangResourceRelation langCode=", langCode)
	for _, rr := range resourceRelations {
		if rr.LangCode == langCode {
			nativeLangResourceRelation = rr
			break
		}
		// 检查基本语言代码是否匹配
		if strings.Split(rr.LangCode, "-")[0] == strings.Split(langCode, "-")[0] {
			nativeLangResourceRelation = rr
		}
	}
	for _, rr := range resourceRelations {
		if rr.Id == resourceResult.OriginReourcelationId {
			originResourceRelation = rr
		}
	}
	return originResourceRelation, nativeLangResourceRelation, nil
}

// GetResourceTags 获取资源的标签
// 返回标签字符串数组
func (r *ResourceModel) GetResourceTags(resourceID string) ([]string, error) {
	resource, err := r.GetByIdString(resourceID)
	if err != nil {
		return nil, err
	}
	if resource == nil {
		return nil, errors.New("资源不存在")
	}

	// 如果标签为空，返回空数组
	if resource.Tags == "" {
		return []string{}, nil
	}

	// 解析JSON字符串为字符串数组
	var tags []string
	if err := json.Unmarshal([]byte(resource.Tags), &tags); err != nil {
		logrus.WithError(err).Error("解析资源标签失败")
		return []string{}, nil
	}

	return tags, nil
}

// SetResourceTags 设置资源的标签
// tags: 标签字符串数组
func (r *ResourceModel) SetResourceTags(resourceID string, tags []string) error {
	resource, err := r.GetByIdString(resourceID)
	if err != nil {
		return err
	}
	if resource == nil {
		return errors.New("资源不存在")
	}

	// 将标签数组转换为JSON字符串
	tagsJSON, err := json.Marshal(tags)
	if err != nil {
		logrus.WithError(err).Error("序列化资源标签失败")
		return err
	}

	// 更新资源的标签字段
	resource.Tags = string(tagsJSON)
	return r.Update(resource, "id = ?", resourceID)
}

// AddResourceTag 添加资源标签
// tag: 要添加的标签
func (r *ResourceModel) AddResourceTag(resourceID string, tag string) error {
	// 获取当前标签
	tags, err := r.GetResourceTags(resourceID)
	if err != nil {
		return err
	}

	// 检查标签是否已存在
	for _, t := range tags {
		if t == tag {
			// 标签已存在，无需添加
			return nil
		}
	}

	// 添加新标签
	tags = append(tags, tag)

	// 保存更新后的标签
	return r.SetResourceTags(resourceID, tags)
}

// RemoveResourceTag 移除资源标签
// tag: 要移除的标签
func (r *ResourceModel) RemoveResourceTag(resourceID string, tag string) error {
	// 获取当前标签
	tags, err := r.GetResourceTags(resourceID)
	if err != nil {
		return err
	}

	// 查找并移除标签
	var newTags []string
	for _, t := range tags {
		if t != tag {
			newTags = append(newTags, t)
		}
	}

	// 如果标签数量没有变化，说明标签不存在
	if len(tags) == len(newTags) {
		return nil
	}

	// 保存更新后的标签
	return r.SetResourceTags(resourceID, newTags)
}

// GetResourcesByTags 根据标签获取资源
// tags: 标签数组，资源必须包含所有指定的标签
// langCode: 语言代码
func (r *ResourceModel) GetResourcesByTags(tags []string, langCode string) ([]*Resource, error) {
	if len(tags) == 0 {
		return []*Resource{}, nil
	}

	// 获取所有资源
	var resources []*Resource

	if langCode != "" {
		// 首先获取与目标语言相关的资源关系
		var resourceRelations []*ResourceRelation
		if err := r.GetList(&resourceRelations, ResourceRelation{LangCode: langCode}); err != nil {
			logrus.WithError(err).Error("获取资源关系失败")
			return nil, err
		}

		// 提取资源ID
		var resourceIds []string
		for _, relation := range resourceRelations {
			resourceIds = append(resourceIds, relation.ResourceId)
		}

		// 如果没有找到资源关系，返回空列表
		if len(resourceIds) == 0 {
			return []*Resource{}, nil
		}

		// 获取资源
		var err error
		resources, err = r.GetByIds(resourceIds)
		if err != nil {
			logrus.WithError(err).Error("获取资源失败")
			return nil, err
		}
	} else {
		// 如果没有指定语言代码，获取所有资源
		err := r.GetList(&resources, "")
		if err != nil {
			return nil, err
		}
	}

	// 过滤包含所有指定标签的资源
	var result []*Resource
	for _, resource := range resources {
		resourceTags, err := r.GetResourceTags(resource.Id)
		if err != nil {
			logrus.WithError(err).Error("获取资源标签失败")
			continue
		}

		// 检查资源是否包含所有指定的标签
		containsAllTags := true
		for _, tag := range tags {
			found := false
			for _, resourceTag := range resourceTags {
				if resourceTag == tag {
					found = true
					break
				}
			}
			if !found {
				containsAllTags = false
				break
			}
		}

		if containsAllTags {
			result = append(result, resource)
		}
	}

	return result, nil
}

// GetAllResources 获取所有资源
// langCode: 语言代码，用于筛选特定语言的资源
// page: 页码，从1开始
// pageSize: 每页数量
// 返回资源列表和总数
func (r *ResourceModel) GetAllResources(langCode string, page, pageSize int) ([]*Resource, int64, error) {
	var resources []*Resource
	var total int64
	var err error

	if langCode != "" {
		// 首先获取与目标语言相关的资源关系
		var resourceRelations []*ResourceRelation
		if err := r.GetList(&resourceRelations, ResourceRelation{LangCode: langCode}); err != nil {
			logrus.WithError(err).Error("获取资源关系失败")
			return nil, 0, err
		}

		// 提取资源ID
		var resourceIds []string
		for _, relation := range resourceRelations {
			resourceIds = append(resourceIds, relation.ResourceId)
		}

		// 如果没有找到资源关系，返回空列表
		if len(resourceIds) == 0 {
			return []*Resource{}, 0, nil
		}

		// 获取资源总数
		total = int64(len(resourceIds))

		// 计算分页
		start := (page - 1) * pageSize
		end := start + pageSize
		if start >= len(resourceIds) {
			return []*Resource{}, total, nil
		}
		if end > len(resourceIds) {
			end = len(resourceIds)
		}

		// 获取分页后的资源ID
		pagedResourceIds := resourceIds[start:end]

		// 获取资源
		resources, err = r.GetByIds(pagedResourceIds)
		if err != nil {
			logrus.WithError(err).Error("获取资源失败")
			return nil, 0, err
		}
	} else {
		// 如果没有指定语言代码，使用原始的查询方式
		query := r.Model(&Resource{})

		// 获取总数
		if err = query.Count(&total).Error; err != nil {
			logrus.WithError(err).Error("获取资源总数失败")
			return nil, 0, err
		}

		// 分页查询
		offset := (page - 1) * pageSize
		if err = query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&resources).Error; err != nil {
			logrus.WithError(err).Error("获取资源列表失败")
			return nil, 0, err
		}
	}

	// 添加审计日志
	logrus.WithFields(logrus.Fields{
		"operation":  "GetAllResources",
		"langCode":   langCode,
		"page":       page,
		"pageSize":   pageSize,
		"totalCount": total,
		"timestamp":  time.Now(),
	}).Info("Resource Query Log")

	return resources, total, nil
}

// GetCategoryTypes 获取所有分类类型信息（使用常量）
func (r *ResourceModel) GetCategoryTypes() []constants.CategoryTypeInfo {
	return constants.GetAllCategoryTypes()
}

// GetCategoryTypeByID 根据ID获取分类类型信息（使用常量）
func (r *ResourceModel) GetCategoryTypeByID(id string) *constants.CategoryTypeInfo {
	return constants.GetCategoryTypeByID(id)
}

// GetCategoryTypeByEnum 根据枚举值获取分类类型信息（使用常量）
func (r *ResourceModel) GetCategoryTypeByEnum(enumValue enum.CategoryType) *constants.CategoryTypeInfo {
	return constants.GetCategoryTypeByEnum(enumValue)
}
