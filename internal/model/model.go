package model

import (
	"context"
	"loop/internal/config"
	"loop/pkg/dbx"
	"time"

	"github.com/google/wire"
	"github.com/sirupsen/logrus"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

var ProviderSet = wire.NewSet(
	NewDbModel, NewDBWrapper, NewDB, NewUserModel, NewResourceModel, NewCategoryModel,
	NewVideoModel, NewNoteModel, NewCollectModel, NewDataCenterModel,
	NewUserAdminModel, NewSeriesAdminModel, NewCategoryAdminModel, NewResourceAdminModel,
	NewSeriesModel, NewVipModel, NewBenefitModel, NewPlanModel,
)

type DbModel struct {
	dbx.DBExtension
}

func NewDbModel(dbx *dbx.DBExtension) *DbModel {
	return &DbModel{
		DBExtension: *dbx,
	}
}

func NewDBWrapper(db *gorm.DB) *dbx.DBExtension {
	return dbx.NewDBWrapper(db)
}

func NewDB(c *config.Config) *gorm.DB {
	log := logrus.StandardLogger()
	log.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
	})
	customLogger := &CustomLogger{logrus: log}
	logrus.Printf("c.DbConfig.Url: %v\n", c.DbConfig.Url)
	db, err := gorm.Open(mysql.Open(c.DbConfig.Url),
		&gorm.Config{
			Logger:                                   customLogger,
			DisableForeignKeyConstraintWhenMigrating: true,
			NamingStrategy: schema.NamingStrategy{
				SingularTable: true,
			},
		})

	if err != nil {
		logrus.Errorf("failed opening connection to sqlite: %v", err)
		panic("failed to connect database " + err.Error())
	}

	if sqlDB, err := db.DB(); err == nil {
		sqlDB.SetMaxIdleConns(c.DbConfig.MaxIdleConns)
		sqlDB.SetMaxOpenConns(c.DbConfig.MaxOpenConns)
		sqlDB.SetConnMaxIdleTime(time.Duration(c.DbConfig.ConnMaxIdleTimeSeconds) * time.Second)
		sqlDB.SetConnMaxLifetime(time.Duration(c.DbConfig.ConnMaxLifetimeSeconds) * time.Second)
	} else {
		logrus.Error(err)

	}

	if err := db.AutoMigrate(Models...); nil != err {
		logrus.Errorf("auto migrate tables failed: %s", err.Error())
	}
	return db
}

// CustomLogger 定义一个自定义的 GORM 日志记录器
type CustomLogger struct {
	logrus *logrus.Logger
}

// LogMode 实现 GORM logger.Interface
func (l *CustomLogger) LogMode(level logger.LogLevel) logger.Interface {
	return l
}

// Info 实现 GORM logger.Interface
func (l *CustomLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	l.logrus.WithContext(ctx).Infof(msg, data...)
}

// Warn 实现 GORM logger.Interface
func (l *CustomLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	l.logrus.WithContext(ctx).Warnf(msg, data...)
}

// Error 实现 GORM logger.Interface
func (l *CustomLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	l.logrus.WithContext(ctx).Errorf(msg, data...)
}

// Trace 实现 GORM logger.Interface
func (l *CustomLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	elapsed := time.Since(begin)
	sql, rows := fc()
	if err != nil {
		l.logrus.WithContext(ctx).WithFields(logrus.Fields{
			"elapsed": elapsed,
			"rows":    rows,
			"error":   err,
		}).Error(sql)
	} else {
		l.logrus.WithContext(ctx).WithFields(logrus.Fields{
			"elapsed": elapsed,
			"rows":    rows,
		}).Info(sql)
	}
}
